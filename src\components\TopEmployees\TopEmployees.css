/* TopEmployees Component Styles */

.employee-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.employee-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.employee-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.employee-avatar {
  width: 40px;
  height: 40px;
  background-color: var(--color-purple);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
}

.employee-name {
  font-weight: 500;
  margin: 0;
}

.employee-email {
  font-size: 0.85rem;
  color: var(--color-text-secondary);
  margin: 0;
}

.employee-score {
  font-weight: 600;
  color: var(--color-accent);
}

.employee-conquests {
  font-size: 0.9rem;
  color: var(--color-text-secondary);
}

.employee-stats {
  text-align: right;
}

.ver-mais-button {
  background: none;
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-family: var(--font-family-sans);
  font-size: 0.8rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  transition: all 0.2s ease;
}

.ver-mais-button:hover {
  background-color: var(--color-border);
  color: var(--color-text-primary);
}
