/* Dreams Component Styles */

.dreams-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.radial-chart-container {
  position: relative;
  width: 150px;
  height: 150px;
  margin-bottom: 1rem;
}

.radial-chart-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.radial-chart-text .percentage {
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--color-text-primary);
  margin: 0;
}

.radial-chart-text .label {
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  margin: 0;
}

.dreams-stats {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
  text-align: center;
}

.dreams-stats > div {
  padding: 0.5rem 1rem;
  background-color: rgba(0, 122, 255, 0.1);
  border-radius: 8px;
}

.dreams-stats .value {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
}

.dreams-stats .label {
  font-size: 0.8rem;
  color: var(--color-text-secondary);
  margin: 0;
}
