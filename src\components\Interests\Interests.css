/* Interests Component Styles */

.treemap {
  display: grid;
  grid-template-columns: 2fr 1.5fr 1fr;
  grid-template-rows: 1.5fr 1fr 1.2fr;
  gap: 10px;
  height: 100%;
  min-height: 200px;
}

.treemap-item {
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  font-weight: 500;
}

.item-familia { 
  grid-column: 1 / 2; 
  grid-row: 1 / 3; 
  background-color: rgba(90, 200, 250, 0.4); 
}

.item-viagens { 
  grid-column: 2 / 4; 
  grid-row: 1 / 2; 
  background-color: rgba(255, 59, 48, 0.3); 
}

.item-filmes { 
  grid-column: 2 / 3; 
  grid-row: 2 / 3; 
  background-color: rgba(175, 82, 222, 0.4); 
}

.item-pets { 
  grid-column: 1 / 2; 
  grid-row: 3 / 4; 
  background-color: rgba(175, 82, 222, 0.3); 
}

.item-esportes { 
  grid-column: 2 / 3; 
  grid-row: 3 / 4; 
  background-color: rgba(52, 199, 89, 0.3); 
}

.item-culinaria { 
  grid-column: 3 / 4; 
  grid-row: 3 / 4; 
  background-color: rgba(255, 149, 0, 0.3); 
}

.item-saude { 
  grid-column: 3 / 4; 
  grid-row: 2 / 3; 
  background-color: rgba(52, 199, 89, 0.4); 
}
