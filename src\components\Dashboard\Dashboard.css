/* Dashboard Component Styles */

/* --- DASHBOARD LAYOUT --- */
.dashboard {
  max-width: 1600px;
  margin: 0 auto;
  position: relative;
}

.logout-button {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-family: var(--font-family-sans);
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  z-index: 10;
}

.logout-button:hover {
  background-color: var(--color-border);
  color: var(--color-text-primary);
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.dashboard-header h1 {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
}

.dashboard-header h1 span {
  font-weight: 400;
  font-size: 1rem;
  color: var(--color-text-secondary);
  display: block;
  margin-top: 4px;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
}

/* --- GRID PLACEMENT --- */
.card-top-employees { grid-column: span 2; }
.card-journey-progress { grid-column: span 2; }
.card-dreams { grid-column: span 1; }
.card-financial-profile { grid-column: span 1; }
.card-age-range { grid-column: span 1; }
.card-financial-situation { grid-column: span 1; }
.card-financial-evolution { grid-column: span 4; }
.card-interests { grid-column: span 2; }
.card-financial-objectives { grid-column: span 2; }
.card-insights { grid-column: span 4; }

/* --- LOADING & ERROR STATES --- */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: var(--color-text-secondary);
  font-style: italic;
}

.error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: var(--color-red);
  text-align: center;
}

/* --- RESPONSIVE STYLES --- */
@media (max-width: 1200px) {
  .dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  .card-financial-evolution, .card-insights { grid-column: span 2; }
  .card-interests, .card-financial-objectives { grid-column: span 2; }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  .card-top-employees, .card-journey-progress, .card-dreams,
  .card-financial-profile, .card-age-range, .card-financial-situation,
  .card-financial-evolution, .card-interests, .card-financial-objectives,
  .card-insights {
    grid-column: span 1;
  }
}
