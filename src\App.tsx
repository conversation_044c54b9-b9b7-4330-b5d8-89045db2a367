import React from 'react';
import { useState } from 'react';
import Dashboard from './components/Dashboard/Dashboard';
import LoginPage from './components/LoginPage/LoginPage';

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  const handleLogin = (accessToken: string) => {
    localStorage.setItem('accessToken', accessToken);
    setIsAuthenticated(true);
    setErrorMessage('');
  };

  const handleError = (message: string) => {
    setErrorMessage(message);
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
  };

  if (!isAuthenticated) {
    return <LoginPage onLogin={handleLogin} onError={handleError} />;
  }

  return <Dashboard onLogout={handleLogout} />;
}

export default App;
