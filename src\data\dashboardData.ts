import { Employee, ProgressItem, BarChartData, HorizontalBarData, InsightData, DreamsData, FinancialEvolutionData } from '../types/dashboard';

export const topEmployees: Employee[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    initials: 'AS',
    avatarColor: '#af52de',
    dcoins: 2450,
    achievements: 14
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    initials: '<PERSON>',
    avatarColor: '#5ac8fa',
    dcoins: 2180,
    achievements: 11
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    initials: 'CO',
    avatarColor: '#ff9500',
    dcoins: 1980,
    achievements: 12
  }
];

export const allEmployees: Employee[] = [
  ...topEmployees,
  {
    id: '4',
    name: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    initials: '<PERSON>',
    avatarColor: '#34c759',
    dcoins: 1850,
    achievements: 9
  },
  {
    id: '5',
    name: '<PERSON>',
    email: '<EMAIL>',
    initials: 'RL',
    avatarColor: '#ff3b30',
    dcoins: 1720,
    achievements: 8
  },
  {
    id: '6',
    name: '<PERSON> <PERSON>',
    email: '<EMAIL>',
    initials: 'JP',
    avatarColor: '#007aff',
    dcoins: 1650,
    achievements: 10
  },
  {
    id: '7',
    name: '<PERSON> Almeida',
    email: '<EMAIL>',
    initials: 'PA',
    avatarColor: '#af52de',
    dcoins: 1580,
    achievements: 7
  },
  {
    id: '8',
    name: 'Camila Rodrigues',
    email: '<EMAIL>',
    initials: 'CR',
    avatarColor: '#5ac8fa',
    dcoins: 1520,
    achievements: 6
  },
  {
    id: '9',
    name: 'Lucas Ferreira',
    email: '<EMAIL>',
    initials: 'LF',
    avatarColor: '#ff9500',
    dcoins: 1480,
    achievements: 8
  },
  {
    id: '10',
    name: 'Amanda Souza',
    email: '<EMAIL>',
    initials: 'AS',
    avatarColor: '#34c759',
    dcoins: 1420,
    achievements: 5
  }
];
export const journeyProgress: ProgressItem[] = [
  { label: 'Início da Jornada', percentage: 82, color: '#007aff' },
  { label: 'Diagnosticar', percentage: 65, color: '#af52de' },
  { label: 'Poupar', percentage: 48, color: '#5ac8fa' },
  { label: 'Sonhar', percentage: 37, color: '#ff9500' }
];

export const dreamsData: DreamsData = {
  percentage: 37.6,
  registered: 850,
  achieved: 320
};

export const financialProfileData: BarChartData[] = [
  { label: 'Superendividados', value: 25 },
  { label: 'Endividados', value: 40 },
  { label: 'Equilibrados', value: 80 },
  { label: 'Investidores', value: 30 }
];

export const ageRangeData: BarChartData[] = [
  { label: '< 18 anos', value: 10, color: '#af52de' },
  { label: '18 a 29 anos', value: 70, color: '#af52de' },
  { label: '30 a 59 anos', value: 90, color: '#af52de' },
  { label: '> 60 anos', value: 20, color: '#af52de' }
];

export const financialSituationData: HorizontalBarData[] = [
  { label: 'Consigo guardar dinheiro', value: 30, percentage: 30 },
  { label: 'Tenho dívidas, mas controlo', value: 25, percentage: 25 },
  { label: 'Não consigo pagar as contas', value: 18, percentage: 18 }
];

export const financialObjectivesData: HorizontalBarData[] = [
  { label: 'Criar uma reserva de emergência', value: 450, percentage: 90 },
  { label: 'Aposentadoria e previdência', value: 388, percentage: 75 },
  { label: 'Investir para um bem/sonho', value: 290, percentage: 60 }
];

export const additionalInsights: InsightData[] = [
  { icon: 'wallet', value: '22%', label: 'Poupam Manualmente', color: '#34c759' },
  { icon: 'mail', value: '45%', label: 'Têm Investimentos Ativos', color: '#5ac8fa' },
  { icon: 'trending-up', value: '61%', label: 'Nunca Controlaram Orçamento', color: '#ff9500' },
  { icon: 'x-circle', value: '34%', label: 'Comportamento Impulsivo', color: '#ff3b30' }
];

export const financialEvolutionData: FinancialEvolutionData = {
  evolution: {
    lessThan12: 0,
    from12To17: 9,
    from18To29: 27,
    from30To59: 29,
    greater60: 2
  },
  situations: [
    {
      month: "2025-07",
      datapoints: [
        { financialProfile: "Undefined", count: 0, percentage: 0 },
        { financialProfile: "Overindebted", count: 0, percentage: 0 },
        { financialProfile: "Indebted", count: 1, percentage: 100 },
        { financialProfile: "Balanced", count: 0, percentage: 0 },
        { financialProfile: "Investor", count: 0, percentage: 0 }
      ]
    },
    {
      month: "2025-08",
      datapoints: [
        { financialProfile: "Undefined", count: 47, percentage: 70.15 },
        { financialProfile: "Overindebted", count: 2, percentage: 2.99 },
        { financialProfile: "Indebted", count: 12, percentage: 17.91 },
        { financialProfile: "Balanced", count: 6, percentage: 8.96 },
        { financialProfile: "Investor", count: 0, percentage: 0 }
      ]
    }
  ]
};