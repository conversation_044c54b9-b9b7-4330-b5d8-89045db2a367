/* JourneyProgress Component Styles */

.progress-list {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.progress-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
}

.progress-bar {
  height: 8px;
  width: 100%;
  background-color: var(--color-border);
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease-out;
}
