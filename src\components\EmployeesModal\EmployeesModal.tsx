import React from 'react';
import { X } from 'lucide-react';
import { Employee } from '../../types/dashboard';
import './EmployeesModal.css';

interface EmployeesModalProps {
  isOpen: boolean;
  onClose: () => void;
  employees: Employee[];
}

const EmployeesModal: React.FC<EmployeesModalProps> = ({ isOpen, onClose, employees }) => {
  if (!isOpen) return null;

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2 className="modal-title">Todos os Colaboradores Engajados</h2>
          <button className="modal-close-button" onClick={onClose}>
            <X size={24} />
          </button>
        </div>
        <div className="modal-body">
          <ul className="modal-employee-list">
            {employees.map((employee, index) => (
              <li key={employee.id} className="modal-employee-item">
                <div className="employee-rank">#{index + 1}</div>
                <div className="employee-info">
                  <div 
                    className="employee-avatar" 
                    style={{ backgroundColor: employee.avatarColor }}
                  >
                    {employee.initials}
                  </div>
                  <div>
                    <div className="employee-name">{employee.name}</div>
                    <div className="employee-email">{employee.email}</div>
                  </div>
                </div>
                <div className="employee-stats">
                  <div className="employee-score">{employee.dcoins.toLocaleString()}</div>
                  <div className="employee-conquests">{employee.achievements} Conquistas</div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default EmployeesModal;