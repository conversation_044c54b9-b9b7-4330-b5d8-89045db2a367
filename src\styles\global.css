/* --- CSS VARIABLES & THEME --- */
:root {
  --color-bg-primary: #12121c;
  --color-bg-secondary: #1c1c28;
  --color-border: #333344;
  --color-text-primary: #f0f0f5;
  --color-text-secondary: #a0a0b0;
  --color-text-tertiary: #6c6c80;

  --color-accent: #007aff;
  --color-green: #34c759;
  --color-teal: #5ac8fa;
  --color-purple: #af52de;
  --color-orange: #ff9500;
  --color-red: #ff3b30;

  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  --border-radius: 12px;
}

/* --- GLOBAL STYLES --- */
body {
  font-family: var(--font-family-sans);
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  margin: 0;
  padding: 2rem;
}

/* --- CARD STYLES --- */
.card {
  background-color: var(--color-bg-secondary);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  border: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.card-header .card-title {
  margin-bottom: 0;
}

.card-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--color-text-primary);
  margin-top: 0;
}

/* --- RESPONSIVE STYLES --- */
@media (max-width: 768px) {
  body { 
    padding: 1rem; 
  }
}
