/* FinancialEvolution Component Styles */

/* Specific styling for the financial evolution card */
.card-financial-evolution {
  min-height: 450px; /* Ensure adequate card height */
}

.line-chart-container {
  position: relative;
  width: 100%;
  height: 350px; /* Increased height for better visibility */
  overflow: visible;
  padding: 20px 10px; /* Increased padding for better spacing */
  box-sizing: border-box;
  min-height: 300px; /* Ensure minimum height */
}

.line-chart-legend {
  display: flex;
  justify-content: center;
  gap: 2rem; /* Increased gap for better spacing */
  margin-top: 1.5rem; /* More space from chart */
  flex-wrap: wrap;
  padding: 0 20px; /* Add horizontal padding */
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.75rem; /* Increased gap between color box and text */
  font-size: 0.9rem; /* Slightly larger font */
  font-weight: 500; /* Medium weight for better readability */
  color: var(--color-text-primary); /* Better contrast */
}

.legend-color-box {
  width: 14px; /* Slightly larger for better visibility */
  height: 14px;
  border-radius: 4px; /* Slightly more rounded */
  flex-shrink: 0; /* Prevent shrinking */
}

/* Tooltip Styles */
.chart-tooltip {
  pointer-events: none;
  z-index: 9999; /* Higher z-index to ensure visibility */
  position: fixed; /* Ensure it's positioned correctly */
}

.tooltip-content {
  background: rgba(0, 0, 0, 0.95); /* Slightly more opaque */
  color: white;
  padding: 12px 16px; /* Increased padding */
  border-radius: 8px; /* More rounded corners */
  font-size: 0.85rem; /* Slightly larger font */
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4); /* Enhanced shadow */
  white-space: nowrap;
  border: 1px solid rgba(255, 255, 255, 0.1); /* Subtle border */
  backdrop-filter: blur(4px); /* Modern blur effect */
}

.tooltip-title {
  font-weight: 600;
  margin-bottom: 4px;
  color: #ffffff;
}

.tooltip-profile {
  font-weight: 500;
  margin-bottom: 4px;
  color: #e0e0e0;
}

.tooltip-stats {
  font-size: 0.75rem;
  color: #cccccc;
}

.tooltip-stats div {
  margin: 2px 0;
}

/* Loading and Error States */
.loading-state, .error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--color-text-secondary);
  font-style: italic;
}

.error-state {
  color: #ff3b30;
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--color-text-secondary);
  font-style: italic;
}

/* --- RESPONSIVE STYLES --- */
@media (max-width: 1200px) {
  .card-financial-evolution {
    min-height: 420px;
  }

  .line-chart-container {
    height: 320px;
    padding: 18px 8px;
  }
}

@media (max-width: 768px) {
  .card-financial-evolution {
    min-height: 380px;
  }

  .line-chart-legend {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }

  .line-chart-container {
    height: 280px;
    padding: 15px 5px;
  }

  .tooltip-content {
    font-size: 0.75rem;
    padding: 6px 10px;
  }
}

@media (max-width: 480px) {
  .card-financial-evolution {
    min-height: 350px;
  }

  .line-chart-container {
    height: 250px;
    padding: 12px 3px;
  }

  .legend-item {
    font-size: 0.8rem;
  }
}
