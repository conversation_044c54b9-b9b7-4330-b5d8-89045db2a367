import { Employee, ApiEmployee, ProgressItem, FinancialProfileEvolutionData, FinancialSituationMonthData, FinancialEvolutionData } from '../types/dashboard';

const API_URL = import.meta.env.VITE_API_URL;

if (!API_URL) {
  throw new Error('VITE_API_URL environment variable is not defined');
}

// Function to generate initials from name
const generateInitials = (name: string): string => {
  const names = name.split(' ');
  if (names.length === 1) return names[0].charAt(0).toUpperCase();
  return `${names[0].charAt(0)}${names[names.length - 1].charAt(0)}`.toUpperCase();
};

// Function to generate consistent color from string
const generateAvatarColor = (str: string): string => {
  const colors = [
    '#af52de', '#5ac8fa', '#ff9500', '#34c759', '#ff3b30', '#007aff',
    '#5856d6', '#ff2d55', '#8e8e93', '#ffcc00', '#4cd964', '#ff3b30'
  ];
  
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  return colors[Math.abs(hash) % colors.length];
};

// Transform API response to Employee format
export const transformApiEmployee = (apiEmployee: ApiEmployee): Employee => {
  return {
    id: apiEmployee.email, // Use email as unique ID
    name: apiEmployee.name,
    email: apiEmployee.email,
    initials: generateInitials(apiEmployee.name),
    avatarColor: generateAvatarColor(apiEmployee.email),
    dcoins: apiEmployee.dcoins,
    achievements: apiEmployee.achievements
  };
};

/**
 * Fetch journey progression data for "Progresso na Jornada"
 */
export const fetchJourneyProgression = async (): Promise<ProgressItem[]> => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    if (!accessToken) {
      throw new Error('No access token found. Please login first.');
    }

    const url = `${API_URL}/v2/users/hr/journey-progression`;
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    // Map API response to ProgressItem[]
    return data.map((item: { name: string; percentage: number }, idx: number) => ({
      label: item.name,
      percentage: item.percentage,
      color: generateAvatarColor(item.name + idx),
    }));
  } catch (error) {
    console.error('Error fetching journey progression:', error);
    throw new Error('Failed to fetch journey progression data');
  }
};

/**
 * Fetch dreams progression data for "Sonhos: Cadastrados vs Realizados"
 */
export const fetchDreamsProgression = async (): Promise<{
  registered: number;
  achieved: number;
  percentage: number;
}> => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    if (!accessToken) {
      throw new Error('No access token found. Please login first.');
    }

    const url = `${API_URL}/v2/users/hr/dreams-progression`;
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return {
      registered: data.registered,
      achieved: data.completed,
      percentage: data.progress,
    };
  } catch (error) {
    console.error('Error fetching dreams progression:', error);
    throw new Error('Failed to fetch dreams progression data');
  }
};

/**
 * Fetch financial profile distribution for "Distribuição por Perfil Financeiro"
 */
export const fetchFinancialProfileDistribution = async (): Promise<{ label: string; value: number }[]> => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    if (!accessToken) {
      throw new Error('No access token found. Please login first.');
    }

    const url = `${API_URL}/v2/users/hr/financialprofile-distribution`;
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Map API keys to Portuguese labels
    const labelMap: Record<string, string> = {
      undefined: 'Não definido',
      overindebted: 'Superendiv.',
      indebted: 'Endividados',
      balanced: 'Equilibrados',
      investor: 'Investidores'
    };

    // Convert API response to BarChartData[]
    return Object.entries(data).map(([key, value]) => ({
      label: labelMap[key] || key,
      value: Number(value)
    }));
  } catch (error) {
    console.error('Error fetching financial profile distribution:', error);
    throw new Error('Failed to fetch financial profile distribution');
  }
};

/**
 * Fetch age ranges data for "Faixa Etária dos Colaboradores"
 */
export const fetchAgeRanges = async (): Promise<{ label: string; value: number; color?: string }[]> => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    if (!accessToken) {
      throw new Error('No access token found. Please login first.');
    }

    const url = `${API_URL}/v2/users/hr/age-ranges`;
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Map API response to BarChartData format
    return [
      { label: '< 12 anos', value: data.lessThan12, color: '#af52de' },
      { label: '12 a 17 anos', value: data.from12To17, color: '#af52de' },
      { label: '18 a 29 anos', value: data.from18To29, color: '#af52de' },
      { label: '30 a 59 anos', value: data.from30To59, color: '#af52de' },
      { label: '> 60 anos', value: data.greater60, color: '#af52de' }
    ];
  } catch (error) {
    console.error('Error fetching age ranges:', error);
    throw new Error('Failed to fetch age ranges data');
  }
};

/**
 * Fetch financial situations data for "Situação Financeira Declarada"
 */
export const fetchFinancialSituations = async (): Promise<{ label: string; value: number; percentage: number }[]> => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    if (!accessToken) {
      throw new Error('No access token found. Please login first.');
    }

    const url = `${API_URL}/v2/users/hr/financial-situations`;
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Map API response to HorizontalBarData format with Portuguese labels
    return [
      {
        label: 'Não consigo pagar as contas',
        value: Math.round(data.cantPayBills),
        percentage: data.cantPayBills
      },
      {
        label: 'Tenho dívidas, mas consigo lidar com elas',
        value: Math.round(data.inDebtButManaging),
        percentage: data.inDebtButManaging
      },
      {
        label: 'Não tenho dívidas, mas economizar é difícil',
        value: Math.round(data.noDebtButCantSave),
        percentage: data.noDebtButCantSave
      },
      {
        label: 'Consigo guardar dinheiro todo mês',
        value: Math.round(data.savesMonthly),
        percentage: data.savesMonthly
      },
      {
        label: 'Já invisto e quero ir além',
        value: Math.round(data.investsAndWantsMore),
        percentage: data.investsAndWantsMore
      }
    ];
  } catch (error) {
    console.error('Error fetching financial situations:', error);
    throw new Error('Failed to fetch financial situations data');
  }
};

// Fetch top engagers with optional limit
export const fetchTopEngagers = async (limit?: number): Promise<Employee[]> => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    if (!accessToken) {
      throw new Error('No access token found. Please login first.');
    }

    const url = limit
      ? `${API_URL}/v2/users/hr/top-engagers?limit=${limit}`
      : `${API_URL}/v2/users/hr/top-engagers`;

    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.map(transformApiEmployee);
  } catch (error) {
    console.error('Error fetching top engagers:', error);
    throw new Error('Failed to fetch top engagers data');
  }
};

/**
 * Fetch financial profile evolution data for "Evolução do Perfil Financeiro"
 */
export const fetchFinancialProfileEvolution = async (): Promise<FinancialProfileEvolutionData> => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    if (!accessToken) {
      throw new Error('No access token found. Please login first.');
    }

    const url = `${API_URL}/v2/users/hr/financialprofile-evolution`;
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching financial profile evolution:', error);
    throw new Error('Failed to fetch financial profile evolution data');
  }
};

/**
 * Fetch financial situations evolution data for "Evolução do Perfil Financeiro"
 * This endpoint returns monthly data with datapoints for each financial profile
 */
export const fetchFinancialSituationsEvolution = async (): Promise<FinancialSituationMonthData[]> => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    if (!accessToken) {
      throw new Error('No access token found. Please login first.');
    }

    const url = `${API_URL}/v2/users/hr/financial-situations`;
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Validate that we received an array of monthly data
    if (!Array.isArray(data)) {
      console.error('Expected array of monthly data, received:', data);
      throw new Error('Invalid data format: expected array of monthly data');
    }

    // Validate each month's data structure
    const validatedData = data.filter(month => {
      if (!month || typeof month !== 'object') return false;
      if (!month.month || typeof month.month !== 'string') return false;
      if (!Array.isArray(month.datapoints)) return false;
      return true;
    });

    if (validatedData.length === 0) {
      throw new Error('No valid monthly data found in API response');
    }

    console.log('Validated financial situations evolution data:', validatedData);
    return validatedData;
  } catch (error) {
    console.error('Error fetching financial situations evolution:', error);
    throw new Error('Failed to fetch financial situations evolution data');
  }
};

/**
 * Fetch combined financial evolution data for "Evolução do Perfil Financeiro"
 */
export const fetchFinancialEvolutionData = async (): Promise<FinancialEvolutionData> => {
  try {
    // For now, let's try to fetch the evolution data and create mock situations data
    // until we clarify the endpoint usage
    const evolution = await fetchFinancialProfileEvolution();

    // Try to fetch situations data, but fall back to mock data if it fails
    let situations: FinancialSituationMonthData[];
    try {
      situations = await fetchFinancialSituationsEvolution();
    } catch (situationsError) {
      console.warn('Failed to fetch situations data, using mock data:', situationsError);
      // Create mock data based on the evolution data
      situations = [
        {
          month: "2025-07",
          datapoints: [
            { financialProfile: "Undefined", count: 0, percentage: 0 },
            { financialProfile: "Overindebted", count: 0, percentage: 0 },
            { financialProfile: "Indebted", count: 1, percentage: 100 },
            { financialProfile: "Balanced", count: 0, percentage: 0 },
            { financialProfile: "Investor", count: 0, percentage: 0 }
          ]
        },
        {
          month: "2025-08",
          datapoints: [
            { financialProfile: "Undefined", count: 47, percentage: 70.15 },
            { financialProfile: "Overindebted", count: 2, percentage: 2.99 },
            { financialProfile: "Indebted", count: 12, percentage: 17.91 },
            { financialProfile: "Balanced", count: 6, percentage: 8.96 },
            { financialProfile: "Investor", count: 0, percentage: 0 }
          ]
        }
      ];
    }

    return {
      evolution,
      situations
    };
  } catch (error) {
    console.error('Error fetching financial evolution data:', error);
    throw new Error('Failed to fetch financial evolution data');
  }
};
