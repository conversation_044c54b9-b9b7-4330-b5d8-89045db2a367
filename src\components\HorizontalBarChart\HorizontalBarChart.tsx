import React from 'react';
import { HorizontalBarData } from '../../types/dashboard';
import './HorizontalBarChart.css';

interface HorizontalBarChartProps {
  data: HorizontalBarData[];
  title: string;
  className: string;
  barColor?: string;
}

const HorizontalBarChart: React.FC<HorizontalBarChartProps> = ({ 
  data, 
  title, 
  className,
  barColor = '#ff9500'
}) => {
  return (
    <article className={`card ${className}`}>
      <h2 className="card-title">{title}</h2>
      <div className="horizontal-bar-list">
        {data.map((item, index) => (
          <div key={index} className="horizontal-bar-item">
            <div className="horizontal-bar-label-container">
              <span>{item.label}</span>
              <span>{typeof item.value === 'number' && item.value > 100 ? item.value : `${item.percentage}%`}</span>
            </div>
            <div className="horizontal-progress-bar">
              <div 
                className="horizontal-progress-bar-fill" 
                style={{ 
                  width: `${item.percentage}%`,
                  backgroundColor: barColor
                }}
              />
            </div>
          </div>
        ))}
      </div>
    </article>
  );
};

export default HorizontalBarChart;