/* AdditionalInsights Component Styles */

.card.card-insights {
  background-color: transparent;
  border: none;
  padding: 0;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  width: 100%;
}

.insight-item {
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.insight-icon {
  background-color: var(--color-border);
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.insight-value {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
}

.insight-label {
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  margin: 0;
}

/* --- RESPONSIVE STYLES --- */
@media (max-width: 768px) {
  .insights-grid {
    grid-template-columns: 1fr;
  }
}
