import React, { useState } from 'react';
import { Eye, EyeOff, Lock, Mail } from 'lucide-react';
import axios from 'axios';
import './LoginPage.css';

interface LoginPageProps {
  onLogin: (accessToken: string) => void;
  onError: (message: string) => void;
}

interface ApiErrorResponse {
  code: number;
  message: string;
}

const LoginPage: React.FC<LoginPageProps> = ({ onLogin, onError }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_URL}/v2/auth/hr/login`,
        {
          email,
          password
        }
      );
      
       // Success
      const { access } = response.data;
      localStorage.setItem('accessToken', access);
      onLogin(access);
    } catch (err: unknown) {
      // Set a generic fallback message first
      let errorMessage = 'An unexpected error occurred. Please try again.';

      // Use axios's type guard to safely access response properties
      if (axios.isAxiosError<ApiErrorResponse>(err)) {
        // The most important check: does the API response contain the data and message we expect?
        if (err.response?.data?.message) {
          // If yes, use the message directly from the API. This is our primary source of truth.
          errorMessage = err.response.data.message;
        }
      }

      // Now, update the state and call the parent component's error handler
      setError(errorMessage);
      onError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="login-header">
          <div className="logo-container">
            <img src="/dinbora.svg" alt="Dinbora" className="logo" />
          </div>
          <h1 className="login-title">Dinbora Business</h1>
          <p className="login-subtitle">Acesso para Recursos Humanos</p>
        </div>

        <form onSubmit={handleSubmit} className="login-form">
          <div className="form-group">
            <label htmlFor="email" className="form-label">
              Email corporativo
            </label>
            <div className="input-wrapper">
              <Mail className="input-icon" size={20} />
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="form-input"
                required
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="password" className="form-label">
              Senha
            </label>
            <div className="input-wrapper">
              <Lock className="input-icon" size={20} />
              <input
                id="password"
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Digite sua senha"
                className="form-input"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="password-toggle"
              >
                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>
          </div>

          <div className="form-options">
            <label className="checkbox-wrapper">
              <input type="checkbox" className="checkbox" />
              <span className="checkbox-label">Manter-me conectado</span>
            </label>
            <a href="#" className="forgot-password">
              Esqueceu a senha?
            </a>
          </div>

          <button
            type="submit"
            disabled={isLoading || !email || !password}
            className="login-button"
          >
            {isLoading ? (
              <div className="loading-spinner" />
            ) : (
              'Acessar Dashboard'
            )}
          </button>

          {error && (
            <div className="error-message">
              {error}
            </div>
          )}
        </form>

        <div className="login-footer">
          <p className="footer-text">
            Precisa de ajuda? Entre em contato com o{' '}
            <a href="#" className="support-link">suporte técnico</a>
          </p>
        </div>
      </div>

      <div className="login-background">
        <div className="background-pattern"></div>
      </div>
    </div>
  );
};

export default LoginPage;
